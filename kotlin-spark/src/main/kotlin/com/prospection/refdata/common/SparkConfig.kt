package com.prospection.refdata.common

import org.apache.spark.SparkConf

object SparkConfig {
    private val ENVIRONMENTS = arrayOf("dev", "tst", "int", "uat", "prd")
    private val BUCKET_COMMONS = mapOf(
        "spark.hadoop.fs.s3a.bucket.pd-au-%s-common.endpoint" to "s3.ap-southeast-2.amazonaws.com",
        "spark.hadoop.fs.s3a.bucket.pd-jp-%s-common.endpoint" to "s3.ap-northeast-1.amazonaws.com",
        "spark.hadoop.fs.s3a.bucket.pd-us-%s-common.endpoint" to "s3.us-west-2.amazonaws.com",
    )

    /*
     * This method is used to get the Spark configuration for the cloud environment.
     */
    fun getCloudSparkConfig(): SparkConf {
        val sparkConfig = getDefaultSparkConfig()
            .set(
                "spark.hadoop.fs.s3a.aws.credentials.provider",
                "software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider"
            )
            .set("spark.hadoop.fs.s3a.endpoint", "s3.ap-southeast-2.amazonaws.com")
            .set("spark.hadoop.fs.s3a.bucket.prospection-data-lake-ap-northeast-1.endpoint", "s3.ap-northeast-1.amazonaws.com")
            .set("spark.hadoop.fs.s3a.bucket.prospection-data-lake-us-west-2.endpoint", "s3.us-west-2.amazonaws.com")

        ENVIRONMENTS.forEach { env ->
            BUCKET_COMMONS.forEach { (bucket, region) ->
                sparkConfig.set(String.format(bucket, env), region)
            }
        }

        return sparkConfig
    }

    fun getDefaultSparkConfig(): SparkConf = SparkConf()
        .set("spark.master", "local[*]")
        .set("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        .set("spark.hadoop.fs.s3a.fast.upload", "true")
        .set("spark.hadoop.fs.s3a.fast.upload.buffer", "bytebuffer")
        .set("spark.testing.memory", "471859200")
        // Disable native libraries for lambda environments
        .set("spark.hadoop.io.native.lib.available", "false")
        .set("spark.hadoop.fs.s3a.impl.disable.cache", "true")
        // Disable Hadoop native code loading to prevent ClassNotFoundException
        .set("spark.driver.extraJavaOptions", "-Djava.io.tmpdir=/tmp -XX:+UseG1GC -XX:+UseContainerSupport -Djava.library.path= -Dhadoop.home.dir=/tmp")
        .set("spark.executor.extraJavaOptions", "-Djava.io.tmpdir=/tmp -Djava.library.path= -Dhadoop.home.dir=/tmp")
        // Optimised based on https://spark.apache.org/docs/latest/cloud-integration.html
        // and https://hadoop.apache.org/docs/r3.1.2/hadoop-aws/tools/hadoop-aws/committers.html
        .set("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", "2")
        .set("spark.hadoop.mapreduce.fileoutputcommitter.cleanup-failures.ignored", "true")
        .set("spark.hadoop.parquet.enable.summary-metadata", "false")
        .set("spark.sql.parquet.mergeSchema", "false")
        .set("spark.sql.parquet.filterPushdown", "true")
        .set("spark.sql.hive.metastorePartitionPruning", "true")
        .set("spark.hadoop.fs.s3a.commiter.staging.conflict-mode", "replace")
        .set("spark.hadoop.fs.s3a.committer.name", "magic")
        .set(
            "spark.sql.sources.commitProtocolClass",
            "org.apache.spark.internal.io.cloud.PathOutputCommitProtocol"
        )
        .set(
            "spark.sql.parquet.output.committer.class",
            "org.apache.spark.internal.io.cloud.BindingParquetOutputCommitter"
        )
        .set(
            "spark.hadoop.mapreduce.outputcommitter.factory.scheme.s3a",
            "org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory"
        )
        // Configure temp directories for containerized environments
        .set("spark.local.dir", "/tmp/spark")
        .set("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
}