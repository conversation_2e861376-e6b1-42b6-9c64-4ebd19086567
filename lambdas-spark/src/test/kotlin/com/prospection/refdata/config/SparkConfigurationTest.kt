package com.prospection.refdata.config

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow

class SparkConfigurationTest {

    @Test
    fun `should initialize Spark session without native library errors`() {
        // Set system properties to disable native libraries before Spark initialization
        System.setProperty("hadoop.home.dir", "/tmp")
        System.setProperty("java.library.path", "")
        System.setProperty("hadoop.native.lib", "false")
        
        assertDoesNotThrow {
            val spark = SparkConfiguration.spark
            assert(spark.sparkContext().isLocal)
            println("Spark session initialized successfully")
            println("Spark version: ${spark.version()}")
            println("Hadoop configuration - io.native.lib.available: ${spark.sparkContext().hadoopConfiguration().get("io.native.lib.available", "not set")}")
        }
    }
}
